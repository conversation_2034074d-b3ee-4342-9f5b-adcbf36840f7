import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import {
  CreateConversationThreadDto,
  UpdateConversationThreadDto,
  ConversationThreadResponseDto,
  GetConversationThreadsQueryDto,
  GetThreadMessagesQueryDto,
  ThreadMessageResponseDto,
} from '../dto/conversation-thread.dto';
import { PaginatedResult } from '@/common/response';
import { CHAT_ERROR_CODES } from '../exceptions';

@Injectable()
export class ConversationThreadService {
  private readonly logger = new Logger(ConversationThreadService.name);

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Create a new conversation thread
   */
  @Transactional()
  async create(createDto: CreateConversationThreadDto, userId: number): Promise<ConversationThreadResponseDto> {
    try {
      const query = `
        INSERT INTO user_conversation_thread (name, user_id, created_at, updated_at)
        VALUES ($1, $2, $3, $3)
        RETURNING thread_id, name, created_at, updated_at
      `;

      const now = Date.now();
      const result = await this.dataSource.query(query, [createDto.name, userId, now]);

      if (!result || result.length === 0) {
        throw new AppException(
          CHAT_ERROR_CODES.THREAD_CREATION_FAILED,
          'Failed to create conversation thread in database',
        );
      }

      const thread = result[0];
      this.logger.log(`Created conversation thread ${thread.thread_id} for user ${userId}`);

      return plainToInstance(ConversationThreadResponseDto, {
        threadId: thread.thread_id,
        name: thread.name,
        createdAt: parseInt(thread.created_at),
        updatedAt: parseInt(thread.updated_at),
      }, { excludeExtraneousValues: true });

    } catch (error) {
      this.logger.error(`Error creating conversation thread: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        CHAT_ERROR_CODES.THREAD_CREATION_FAILED,
        `Failed to create conversation thread: ${error.message}`,
      );
    }
  }

  /**
   * Get all conversation threads for a user with pagination
   */
  async findAllByUser(queryDto: GetConversationThreadsQueryDto, userId: number): Promise<PaginatedResult<ConversationThreadResponseDto>> {
    const { page = 1, limit = 10, search, sortBy = 'created_at', sortDirection = 'DESC' } = queryDto;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    let whereClause = 'WHERE user_id = $1';
    const queryParams: any[] = [userId];
    let paramIndex = 2;

    if (search) {
      whereClause += ` AND name ILIKE $${paramIndex}`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Build ORDER BY clause
    const allowedSortFields = ['created_at', 'updated_at', 'name'];
    const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const safeSortDirection = ['ASC', 'DESC'].includes(sortDirection) ? sortDirection : 'DESC';
    const orderClause = `ORDER BY ${safeSortBy} ${safeSortDirection}`;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_conversation_thread
      ${whereClause}
    `;

    const countResult = await this.dataSource.query(countQuery, queryParams);
    const total = parseInt(countResult[0].total);

    // Get threads with pagination
    const threadsQuery = `
      SELECT thread_id, name, created_at, updated_at
      FROM user_conversation_thread
      ${whereClause} AND deleted_at IS NULL
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const threadsResult = await this.dataSource.query(threadsQuery, queryParams);

    const threads: ConversationThreadResponseDto[] = threadsResult.map((thread: any) =>
      plainToInstance(ConversationThreadResponseDto, {
        threadId: thread.thread_id,
        name: thread.name,
        createdAt: parseInt(thread.created_at),
        updatedAt: parseInt(thread.updated_at),
      }, { excludeExtraneousValues: true })
    );

    const totalPages = Math.ceil(total / limit);

    this.logger.debug(`Retrieved ${threads.length} threads for user ${userId} (page ${page}/${totalPages})`);

    return {
      items: threads,
      meta: {
        totalItems: total,
        itemCount: threads.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        hasItems: total > 0,
      },
    };
  }

  /**
   * Get a specific conversation thread by ID
   */
  async findOne(threadId: string, userId: number): Promise<ConversationThreadResponseDto> {
    const query = `
      SELECT thread_id, name, created_at, updated_at
      FROM user_conversation_thread
      WHERE thread_id = $1 AND user_id = $2 AND deleted_at IS NULL
    `;

    const result = await this.dataSource.query(query, [threadId, userId]);

    if (!result || result.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_NOT_FOUND,
        `Conversation thread ${threadId} not found or access denied`,
      );
    }

    const thread = result[0];

    return plainToInstance(ConversationThreadResponseDto, {
      threadId: thread.thread_id,
      name: thread.name,
      createdAt: parseInt(thread.created_at),
      updatedAt: parseInt(thread.updated_at),
    }, { excludeExtraneousValues: true });
  }

  /**
   * Update a conversation thread
   */
  async update(threadId: string, userId: number, updateDto: UpdateConversationThreadDto): Promise<ConversationThreadResponseDto> {
    // Check if thread exists and belongs to user
    await this.findOne(threadId, userId);

    const now = Date.now();
    const query = `
      UPDATE user_conversation_thread
      SET name = COALESCE($1, name), updated_at = $2
      WHERE thread_id = $3 AND user_id = $4 AND deleted_at IS NULL
      RETURNING thread_id, name, created_at, updated_at
    `;

    const result = await this.dataSource.query(query, [updateDto.name, now, threadId, userId]);

    if (!result || result.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_NOT_FOUND,
        `Conversation thread ${threadId} not found or access denied`,
      );
    }

    const thread = result[0];
    this.logger.log(`Updated conversation thread ${threadId} for user ${userId}`);

    return plainToInstance(ConversationThreadResponseDto, {
      threadId: thread.thread_id,
      name: thread.name,
      createdAt: parseInt(thread.created_at),
      updatedAt: parseInt(thread.updated_at),
    }, { excludeExtraneousValues: true });
  }

  /**
   * Delete a conversation thread
   */
  async remove(threadId: string, userId: number): Promise<void> {
    // Check if thread exists and belongs to user
    await this.findOne(threadId, userId);

    const query = `
      UPDATE user_conversation_thread
      SET deleted_at = $3
      WHERE thread_id = $1 AND user_id = $2
    `;

    await this.dataSource.query(query, [threadId, userId, Date.now()]);

    this.logger.log(`Deleted conversation thread ${threadId} for user ${userId}`);
  }

  /**
   * Get messages for a specific thread with pagination
   */
  async getThreadMessages(
    threadId: string,
    userId: number,
    queryDto: GetThreadMessagesQueryDto
  ): Promise<PaginatedResult<ThreadMessageResponseDto>> {
    // First verify the thread exists and belongs to the user
    await this.findOne(threadId, userId);

    const { page = 1, limit = 50, role, sortBy = 'timestamp', sortDirection = 'DESC' } = queryDto;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    let whereClause = 'WHERE m.thread_id = $1';
    const queryParams: any[] = [threadId];
    let paramIndex = 2;

    if (role) {
      whereClause += ` AND m.role = $${paramIndex}`;
      queryParams.push(role);
      paramIndex++;
    }

    // Build ORDER BY clause for messages
    const allowedMessageSortFields = ['timestamp', 'role'];
    const safeSortBy = allowedMessageSortFields.includes(sortBy) ? sortBy : 'timestamp';
    const safeSortDirection = ['ASC', 'DESC'].includes(sortDirection) ? sortDirection : 'DESC';
    const orderClause = `ORDER BY m.${safeSortBy} ${safeSortDirection}`;

    // Get total count of messages
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_messages m
      ${whereClause}
    `;

    const countResult = await this.dataSource.query(countQuery, queryParams);
    const total = parseInt(countResult[0]?.total || '0');

    // Get messages with pagination
    const messagesQuery = `
      SELECT
        m.message_id,
        m.thread_id,
        m.role,
        m.content,
        m.timestamp,
        m.updated_at
      FROM user_messages m
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const messagesResult = await this.dataSource.query(messagesQuery, queryParams);

    // Simple mapping - return data as-is from database (input = output principle)
    const messages: ThreadMessageResponseDto[] = messagesResult.map((message: any) => {
      const updatedAt = message.updated_at ? parseInt(message.updated_at) : parseInt(message.timestamp);
      const timestamp = parseInt(message.timestamp);

      // Process content to add editedAt timestamp to text blocks if message was updated
      const processedContent = this.processContentForResponse(message.content, updatedAt, timestamp);

      return plainToInstance(ThreadMessageResponseDto, {
        messageId: message.message_id,
        threadId: message.thread_id,
        role: message.role,
        content: processedContent, // Return processed content with editedAt timestamps
        timestamp: timestamp,
        updatedAt: updatedAt,
      }, { excludeExtraneousValues: true });
    });

    const totalPages = Math.ceil(total / limit);

    this.logger.debug(`Retrieved ${messages.length} messages for thread ${threadId} (page ${page}/${totalPages})`);

    return {
      items: messages,
      meta: {
        totalItems: total,
        itemCount: messages.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        hasItems: total > 0,
      },
    };
  }

  /**
   * Process message content to add editedAt timestamp to text blocks if message was updated
   * @param content Original message content from database
   * @param updatedAt Updated timestamp from database
   * @param timestamp Original timestamp from database
   * @returns Processed content with editedAt timestamps
   */
  private processContentForResponse(content: any, updatedAt: number, timestamp: number): any {
    // If message was never updated, return content as-is
    if (updatedAt === timestamp) {
      return content;
    }

    // If content has contentBlocks, process them to add editedAt to text blocks
    if (content && content.contentBlocks && Array.isArray(content.contentBlocks)) {
      const processedContentBlocks = content.contentBlocks.map((block: any) => {
        // Add editedAt timestamp to text blocks that have edited: true
        if (block.type === 'text' && block.edited === true) {
          return {
            ...block,
            editedAt: updatedAt
          };
        }
        return block;
      });

      return {
        ...content,
        contentBlocks: processedContentBlocks
      };
    }

    return content;
  }
}
