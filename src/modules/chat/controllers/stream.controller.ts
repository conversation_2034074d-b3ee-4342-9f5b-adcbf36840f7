import {
  BadRequestException,
  Controller,
  Get,
  Lo<PERSON>,
  <PERSON>m,
  ParseUUIDPip<PERSON>,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiBearerAuth, ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { RedisService } from '@shared/services/redis.service';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '@modules/chat/exceptions';

@ApiTags('Chat')
@ApiBearerAuth('JWT-auth')
@Controller('chat/stream')
@ApiExtraModels(

)
export class StreamController {
  private readonly logger = new Logger(StreamController.name);

  constructor(private readonly redisService: RedisService) {}

  @Get('events/:threadId/:runId')
  @ApiOperation({
    summary: 'Stream chat events via Server-Sent Events',
    description:
      'Establishes an SSE connection to stream real-time chat events for a specific thread. Resumes from the last known message ID.',
  })
  @ApiParam({
    name: 'threadId',
    description: 'Chat thread ID to stream events for',
  })
  @ApiParam({
    name: 'runId',
    description: 'Run ID to stream events for',
  })
  @ApiQuery({
    name: 'from',
    description:
      'Optional message ID to resume from (Last-Event-ID). If not provided, starts from live messages.',
    required: false,
  })
  async streamEvents(
    @Req() req: Request,
    @Res() res: Response,
    @Param(
      'threadId',
      new ParseUUIDPipe({
        exceptionFactory: (errors) => {
          new Logger('StreamController').error(errors);
          throw new AppException(CHAT_ERROR_CODES.INVALID_THREAD_ID);
        },
      }),
    )
    threadId: string,
    @Param(
      'runId',
      new ParseUUIDPipe({
        exceptionFactory: (errors) => {
          new Logger('StreamController').error(errors);
          throw new AppException(CHAT_ERROR_CODES.INVALID_RUN_ID);
        },
      }),
    )
    runId: string,
    @Query('from') fromMessageId?: string,
  ): Promise<void> {
    if (!threadId) {
      throw new BadRequestException('threadId is required');
    }

    this.logger.log(`🔥 Starting chat SSE stream for thread ${threadId}`);

    res.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
      'X-Accel-Buffering': 'no',
    });

    res.write(`event: connected\n`);
    res.write(
      `data: ${JSON.stringify({
        threadId,
        from: fromMessageId || 'live',
        timestamp: Date.now(),
      })}\n\n`,
    );

    const streamKey = `agent_stream:${threadId}:${runId}`;

    try {
      // We no longer need to manage consumer groups here.
      // We will read directly from the stream.
      await this.consumeStreamMessages(res, streamKey, threadId, fromMessageId);
    } catch (error) {
      this.logger.error(`💥 Stream failed for thread ${threadId}:`, error);
      if (!res.writableEnded) {
        res.write(`event: error\n`);
        res.write(
          `data: ${JSON.stringify({
            error: 'Failed to initialize stream',
            threadId,
          })}\n\n`,
        );
        res.end();
      }
    }

    req.on('close', () => {
      this.logger.log(`🔌 Client disconnected from chat thread ${threadId}`);
    });
  }

  private async consumeStreamMessages(
    res: Response,
    streamKey: string,
    threadId: string,
    fromMessageId?: string,
  ): Promise<void> {
    const client = this.redisService.getDuplicateClient();

    const sendMessage = (id: string, fields: string[]): boolean => {
      const payload = this.parseFields(fields);
      res.write(`id: ${id}\n`);
      res.write(`data: ${JSON.stringify(payload)}\n\n`);
      this.logger.debug(`-> Sent message ${payload.event} for thread ${threadId}`);
      if (
        // payload.event === 'llm_stream_end' ||
        // payload.event === 'stream_cancelled'
        payload.event === 'stream_session_end'
      ) {
        this.logger.log(
          `🏁 Stream ended for thread ${threadId} via event: ${payload.event}`,
        );
        res.end();
        return true;
      }
      return false;
    };

    // If client doesn't provide an ID, we start from the beginning of the stream's history.
    // IMPORTANT: See architectural note below about using '0-0'.
    let lastId = fromMessageId || '0-0';

    this.logger.log(
      `Starting consumption for ${threadId}. Initial position: ${lastId}`,
    );

    // 1. CATCH-UP READ (Read unconsumed messages)
    // This is a non-blocking read to get any messages that have already arrived.
    // This closes the race condition for new connections.
    try {
      const catchUpResponse = await client.xread('STREAMS', streamKey, lastId);
      if (catchUpResponse) {
        // @ts-ignore
        const [[, messages]] = catchUpResponse;
        this.logger.log(
          `Replaying ${messages.length} unconsumed messages for ${threadId}.`,
        );
        for (const [id, fields] of messages) {
          if (sendMessage(id, fields)) return; // Stop if stream ends
          lastId = id; // Update the last seen ID
        }
      }
    } catch (error) {
      this.logger.error(
        `💥 Error during stream catch-up for ${threadId}:`,
        error.message,
      );
      if (!res.writableEnded) res.end();
      return;
    }

    // 2. LIVE READ (Listen for new messages)
    // Now we enter the blocking loop to wait for new messages from where we left off.
    while (!res.writableEnded) {
      try {
        const liveResponse = await client.xread(
          'BLOCK',
          5000,
          'STREAMS',
          streamKey,
          lastId, // Start from the last message we saw in the catch-up
        );

        if (liveResponse) {
          // @ts-ignore
          const [[, messages]] = liveResponse;
          for (const [id, fields] of messages) {
            if (sendMessage(id, fields)) return; // Stop if stream ends
            lastId = id; // Update the last seen ID for the next loop iteration
          }
        }
      } catch (error) {
        this.logger.error(
          `💥 Error during live stream consumption for ${threadId}:`,
          error.message,
        );
        if (!res.writableEnded) res.end();
        break;
      }
    }

    await client.quit();
    this.logger.log(`🛑 SSE consumption loop ended for thread ${threadId}`);
  }

  private parseFields(fields: string[]): Record<string, any> {
    const obj: Record<string, any> = {};
    for (let i = 0; i < fields.length; i += 2) {
      const key = fields[i];
      try {
        obj[key] = JSON.parse(fields[i + 1]);
      } catch {
        obj[key] = fields[i + 1];
      }
    }
    return obj;
  }
}
