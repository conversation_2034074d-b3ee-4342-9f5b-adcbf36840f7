import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class ToolCallDecisionDto {
  @ApiProperty({
    description: "The type of content block, fixed to 'tool_call_decision'.",
    enum: ['tool_call_decision'],
    example: 'tool_call_decision',
  })
  @IsEnum(['tool_call_decision'])
  @IsNotEmpty()
  type: 'tool_call_decision';

  @ApiProperty({
    description: 'The decision for the proposed tool call.',
    enum: ['yes', 'no', 'always'],
    example: 'yes',
  })
  @IsEnum(['yes', 'no', 'always'])
  @IsNotEmpty()
  decision: 'yes' | 'no' | 'always';
}