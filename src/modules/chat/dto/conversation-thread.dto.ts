import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsInt, Min, Max } from 'class-validator';
import { Type, Expose } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { MessageRequestDto } from './message-request.dto';

export class CreateConversationThreadDto {
  @ApiProperty({
    description: 'Name of the conversation thread',
    example: 'My AI Assistant Chat',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateConversationThreadDto {
  @ApiPropertyOptional({
    description: 'Name of the conversation thread',
    example: 'Updated Chat Name',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  name?: string;
}

export class ConversationThreadResponseDto {
  @ApiProperty({
    description: 'Unique thread identifier',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @Expose()
  threadId: string;

  @ApiProperty({
    description: 'Name of the conversation thread',
    example: 'My AI Assistant Chat',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Creation timestamp (epoch milliseconds)',
    example: 1749269123456,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Last update timestamp (epoch milliseconds)',
    example: 1749269123456,
  })
  @Expose()
  updatedAt: number;
}

export class GetConversationThreadsQueryDto extends QueryDto {
}



export class ThreadMessageResponseDto {
  @ApiProperty({
    description: 'Unique message identifier',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @Expose()
  messageId: string;

  @ApiProperty({
    description: 'Thread ID this message belongs to',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @Expose()
  threadId: string;

  @ApiProperty({
    description: 'Role of the message sender',
    example: 'user',
    enum: ['user', 'assistant'],
  })
  @Expose()
  role: string;

  @ApiProperty({
    description: 'Message content - exact same structure as MessageRequestDto (input = output principle)',
    type: () => MessageRequestDto,
  })
  @Expose()
  content: MessageRequestDto;

  @ApiProperty({
    description: 'Message timestamp (epoch milliseconds)',
    example: 1749269123456,
  })
  @Expose()
  timestamp: number;

  @ApiProperty({
    description: 'Message last updated timestamp (epoch milliseconds). Same as timestamp if never modified.',
    example: 1749269123456,
    required: false,
  })
  @Expose()
  updatedAt?: number;
}

export class GetThreadMessagesQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Filter by message role',
    example: 'user',
    enum: ['user', 'assistant'],
  })
  @IsOptional()
  @IsString()
  role?: string;

  // Override default limit for messages
  @ApiProperty({
    description: 'Number of messages per page',
    example: 50,
    default: 50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Max(200)
  @Min(1)
  limit: number = 50;
}


