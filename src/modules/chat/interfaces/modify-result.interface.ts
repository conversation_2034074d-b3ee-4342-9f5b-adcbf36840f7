/**
 * Interface for modify operation results
 */
export interface ModifyResult {
  /**
   * ID of the message that was modified
   */
  modifiedMessageId: string;

  /**
   * Array of message IDs that were deleted as a result of the modification
   */
  deletedMessageIds: string[];

  /**
   * Total count of messages that were deleted
   */
  deletedMessagesCount: number;
}

/**
 * Interface for modification context in run payload metadata
 */
export interface ModificationContext {
  /**
   * Whether the request contains any modifications
   */
  hasModifications: boolean;

  /**
   * ID of the message that was modified (only present if hasModifications is true)
   */
  modifiedMessageId?: string;

  /**
   * Array of message IDs that were deleted (only present if hasModifications is true)
   */
  deletedMessageIds?: string[];

  /**
   * Total count of messages that were deleted (only present if hasModifications is true)
   */
  deletedMessagesCount?: number;

  /**
   * Type of modification operation (only present if hasModifications is true)
   */
  operationType?: 'modify_text';
}

/**
 * Interface for modification details in API response
 */
export interface ModificationDetails {
  /**
   * ID of the message that was modified
   */
  modifiedMessageId: string;

  /**
   * Array of message IDs that were deleted as a result of the modification
   */
  deletedMessageIds: string[];

  /**
   * Total count of messages that were deleted
   */
  deletedMessagesCount: number;

  /**
   * Type of modification operation
   */
  operationType: 'modify_text';
}
