import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Error codes for Chat module
 */
export const CHAT_ERROR_CODES = {
  // Agent related errors
  AGENT_NOT_FOUND: new ErrorCode(
    10401,
    'Agent not found',
    HttpStatus.NOT_FOUND,
  ),
  RUN_CREATION_FAILED: new ErrorCode(
    10402,
    'Failed to create chat run',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVALID_AGENT_TYPE: new ErrorCode(
    10403,
    'Invalid agent type',
    HttpStatus.BAD_REQUEST,
  ),
  AGENT_ACCESS_DENIED: new ErrorCode(
    10404,
    'Access denied to agent',
    HttpStatus.FORBIDDEN,
  ),

  // Thread related errors
  INVALID_THREAD_ID: new ErrorCode(
    10405,
    'Invalid thread ID format',
    HttpStatus.BAD_REQUEST,
  ),
  THREAD_VALIDATION_FAILED: new ErrorCode(
    10406,
    'Thread validation failed',
    HttpStatus.BAD_REQUEST,
  ),
  THREAD_NOT_FOUND: new ErrorCode(
    10407,
    'Conversation thread not found',
    HttpStatus.NOT_FOUND,
  ),
  THREAD_FETCH_FAILED: new ErrorCode(
    10408,
    'Failed to fetch conversation thread',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  THREAD_CREATION_FAILED: new ErrorCode(
    10409,
    'Failed to create conversation thread',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  THREAD_UPDATE_FAILED: new ErrorCode(
    10410,
    'Failed to update conversation thread',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  THREAD_DELETE_FAILED: new ErrorCode(
    10411,
    'Failed to delete conversation thread',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  THREAD_ACCESS_DENIED: new ErrorCode(
    10412,
    'Access denied to conversation thread',
    HttpStatus.FORBIDDEN,
  ),

  // Run related errors
  INVALID_RUN_ID: new ErrorCode(
    10413,
    'Invalid run ID format',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_PROVIDER: new ErrorCode(
    10414,
    'Invalid provider configuration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  TOO_MANY_SUPERVISORS: new ErrorCode(
    10415,
    'Cannot have more than 1 supervisor',
    HttpStatus.BAD_REQUEST,
  ),

  // Message related errors
  MESSAGE_PROCESSING_FAILED: new ErrorCode(
    10416,
    'Message processing failed',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  MESSAGES_FETCH_FAILED: new ErrorCode(
    10417,
    'Failed to fetch thread messages',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  MESSAGE_NOT_FOUND: new ErrorCode(
    10418,
    'Message not found',
    HttpStatus.NOT_FOUND,
  ),
  MESSAGE_FORMAT_ERROR: new ErrorCode(
    10425,
    'Message formatting error - invalid content structure',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  MODIFY_TEXT_VALIDATION_FAILED: new ErrorCode(
    10426,
    'Cannot modify message - target message has no text content',
    HttpStatus.BAD_REQUEST,
  ),
  MESSAGE_CASCADE_DELETE_FAILED: new ErrorCode(
    10431,
    'Failed to delete subsequent messages during modification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // File validation errors
  INVALID_FILE_ID_FORMAT: new ErrorCode(
    10427,
    'File ID must be a valid UUID format',
    HttpStatus.BAD_REQUEST,
  ),
  FILE_NOT_FOUND: new ErrorCode(
    10428,
    'File does not exist or is not accessible',
    HttpStatus.BAD_REQUEST,
  ),
  IMAGE_NOT_FOUND: new ErrorCode(
    10429,
    'Image does not exist or is not accessible',
    HttpStatus.BAD_REQUEST,
  ),

  // Reply related errors
  REPLY_TARGET_NOT_FOUND: new ErrorCode(
    10419,
    'Reply target message not found',
    HttpStatus.BAD_REQUEST,
  ),
  REPLY_TARGET_DIFFERENT_THREAD: new ErrorCode(
    10420,
    'Reply target message belongs to different thread',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_REPLY_TARGET: new ErrorCode(
    10421,
    'Invalid reply target message',
    HttpStatus.BAD_REQUEST,
  ),

  // Stream related errors
  STREAM_CONNECTION_FAILED: new ErrorCode(
    10422,
    'Failed to establish stream connection',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  STREAM_READ_FAILED: new ErrorCode(
    10423,
    'Failed to read from stream',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // General errors
  INVALID_INPUT: new ErrorCode(
    10424,
    'Invalid input data for chat operation',
    HttpStatus.BAD_REQUEST,
  ),

  AGENT_CONFIG_BUILD_FAILED: new ErrorCode(
    10425,
    'Failed to build agent configuration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INVALID_TOOL_CALL_DECISION: new ErrorCode(
    10426,
    'More than 1 tool_call_decision blocks',
    HttpStatus.BAD_REQUEST,
  ),
};
