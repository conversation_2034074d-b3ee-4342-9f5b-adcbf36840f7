import { Controller, Get, HttpCode, HttpStatus, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { StatisticsUserService } from '../services/statistics-user.service';
import { UserStorageResponseDto, UserStatisticsResponseDto } from '../../dto/statistics-response.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { STATISTICS_ERROR_CODES } from '../../exceptions/statistics.exception';
import { ErrorCode } from '@common/exceptions';

/**
 * Controller xử lý API user liên quan đến thống kê dữ liệu
 * Cung cấp endpoint để user lấy thống kê dung lượng dữ liệu của chính mình
 */
@ApiTags(SWAGGER_API_TAGS.DATA_STATISTICS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@ApiExtraModels(ApiResponseDto, UserStorageResponseDto, UserStatisticsResponseDto)
@Controller('user/statistics')
export class StatisticsUserController {
  constructor(private readonly statisticsUserService: StatisticsUserService) {}

  /**
   * Lấy thống kê dung lượng dữ liệu của người dùng hiện tại
   */
  @Get('storage')
  @ApiOperation({
    summary: 'Lấy thống kê dung lượng dữ liệu của người dùng hiện tại',
    description: 'API này cho phép user xem thông tin sử dụng dung lượng dữ liệu của chính mình bao gồm giới hạn, đã sử dụng, còn lại và phần trăm sử dụng'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê dung lượng thành công',
    schema: ApiResponseDto.getSchema(UserStorageResponseDto),
  })
  @ApiErrorResponse(
    STATISTICS_ERROR_CODES.USER_USAGE_NOT_FOUND,
    STATISTICS_ERROR_CODES.STATISTICS_SERVICE_ERROR
  )
  async getUserStorageUsage(@CurrentUser() user: JwtPayload): Promise<ApiResponseDto<UserStorageResponseDto>> {
    const storageStats = await this.statisticsUserService.getUserStorageUsage(user.id);
    return ApiResponseDto.success(storageStats, 'Lấy thống kê dung lượng dữ liệu thành công');
  }

  /**
   * Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store của user hiện tại
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store của user hiện tại',
    description: 'API này cho phép user xem thống kê số lượng media files, knowledge files, URLs và vector stores của chính mình'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê số lượng thành công',
    schema: ApiResponseDto.getSchema(UserStatisticsResponseDto),
  })
  @ApiErrorResponse(
    STATISTICS_ERROR_CODES.STATISTICS_CALCULATION_ERROR,
    STATISTICS_ERROR_CODES.STATISTICS_SERVICE_ERROR
  )
  async getUserStatistics(@CurrentUser() user: JwtPayload): Promise<ApiResponseDto<UserStatisticsResponseDto>> {
    const statistics = await this.statisticsUserService.getUserStatistics(user.id);
    return ApiResponseDto.success(statistics, 'Lấy thống kê số lượng dữ liệu thành công');
  }
}
