import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { VectorStoreUserService } from '../services';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import {
  AssignFilesDto,
  AssignFilesResponseDto,
  CreateVectorStoreDto,
  DeleteVectorStoresDto,
  QueryVectorStoreDto,
  UpdateVectorStoreDto,
  VectorStoreResponseDto,
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions/error-codes';
import { ErrorCode } from '@common/exceptions';

@ApiTags(SWAGGER_API_TAGS.USER_KNOWLEDGE_FILES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  VectorStoreResponseDto,
  AssignFilesResponseDto,
  CreateVectorStoreDto,
  UpdateVectorStoreDto,
  DeleteVectorStoresDto,
  AssignFilesDto,
  QueryVectorStoreDto
)
@Controller('user/vector-stores')
export class VectorStoreUserController {
  constructor(
    private readonly vectorStoreUserService: VectorStoreUserService,
  ) {}

  /**
   * Tạo vector store mới
   */
  @ApiOperation({
    summary: 'Tạo vector store mới',
    description: 'Tạo một vector store mới để lưu trữ và quản lý các file tri thức'
  })
  @ApiBody({
    type: CreateVectorStoreDto,
    description: 'Thông tin vector store cần tạo',
    examples: {
      'basic': {
        summary: 'Tạo vector store cơ bản',
        description: 'Tạo vector store với tên cơ bản',
        value: {
          name: 'Vector Store của tôi'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createVectorStore(
    @Body() createVectorStoreDto: CreateVectorStoreDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.createVectorStore(
      createVectorStoreDto,
      userId,
    );
    return ApiResponseDto.created(
      result,
      'Tạo vector store thành công.'
    );
  }

  /**
   * Lấy danh sách vector stores
   */
  @ApiOperation({
    summary: 'Lấy danh sách vector stores',
    description: 'Lấy danh sách vector stores với khả năng tìm kiếm và phân trang'
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Tên vector store cần tìm kiếm',
    example: 'vector store'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng kết quả trên một trang',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Trường sắp xếp',
    enum: ['name', 'createdAt', 'storage'],
    example: 'createdAt'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    description: 'Hướng sắp xếp',
    enum: ['ASC', 'DESC'],
    example: 'DESC'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách vector store thành công.',
    schema: ApiResponseDto.getPaginatedSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get()
  @HttpCode(HttpStatus.OK)
  async getVectorStores(
    @Query() queryDto: QueryVectorStoreDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.getVectorStores(
      queryDto,
      userId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách vector store thành công.'
    );
  }

  /**
   * Cập nhật thông tin vector store
   */
  @ApiOperation({
    summary: 'Cập nhật thông tin vector store',
    description: 'Cập nhật thông tin của vector store như tên, dung lượng'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiBody({
    type: UpdateVectorStoreDto,
    description: 'Thông tin cần cập nhật',
    examples: {
      'updateName': {
        summary: 'Cập nhật tên',
        description: 'Cập nhật tên vector store',
        value: {
          name: 'Vector Store mới'
        }
      },
      'updateNameAndStorage': {
        summary: 'Cập nhật tên và dung lượng',
        description: 'Cập nhật cả tên và dung lượng',
        value: {
          name: 'Vector Store mới',
          storage: 2048000
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_UPDATE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  async updateVectorStore(
    @Param('id') id: string,
    @Body() updateVectorStoreDto: UpdateVectorStoreDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.updateVectorStore(
      id,
      updateVectorStoreDto,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Cập nhật vector store thành công.'
    );
  }

  /**
   * Lấy thông tin chi tiết vector store
   */
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết vector store',
    description: 'Lấy thông tin chi tiết của một vector store cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async getVectorStoreDetail(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.getVectorStoreDetail(
      id,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết vector store thành công.'
    );
  }

  /**
   * Gán file vào vector store và lấy file ID từ RAG API với tùy chọn chunk_size và chunk_overlap
   */
  @ApiOperation({
    summary: 'Gán file vào vector store và lấy file ID từ RAG API',
    description: 'Gán các file vào vector store với khả năng tùy chỉnh kích thước chunk (chunk_size) và độ chồng lấp giữa các chunk (chunk_overlap). Mặc định: chunk_size=2000, chunk_overlap=100.'
  })
  @ApiParam({ name: 'id', description: 'ID của vector store' })
  @ApiBody({
    type: AssignFilesDto,
    description: 'Danh sách ID của các file cần gán và thông số xử lý chunk',
    examples: {
      'Basic': {
        summary: 'Gán file với cài đặt mặc định',
        description: 'Sử dụng chunk_size=2000 và chunk_overlap=100 mặc định',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ]
        }
      },
      'WithChunkSettings': {
        summary: 'Gán file với cài đặt chunk tùy chỉnh',
        description: 'Tùy chỉnh kích thước chunk và độ chồng lấp',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ],
          chunkSize: 2000,
          chunkOverlap: 100
        }
      },
      'LargeChunks': {
        summary: 'Gán file với chunk lớn',
        description: 'Sử dụng chunk lớn hơn cho tài liệu dài',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
          ],
          chunkSize: 4000,
          chunkOverlap: 200
        }
      },
      'SmallChunks': {
        summary: 'Gán file với chunk nhỏ',
        description: 'Sử dụng chunk nhỏ hơn cho tài liệu ngắn hoặc cần độ chính xác cao',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
          ],
          chunkSize: 1000,
          chunkOverlap: 50
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đã nhận file ID từ RAG API và gán vào vector store thành công.',
    schema: ApiResponseDto.getSchema(AssignFilesResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_ASSIGN_FILES_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Post(':id/files')
  @HttpCode(HttpStatus.OK)
  async assignFilesToVectorStore(
    @Param('id') id: string,
    @Body() assignFilesDto: AssignFilesDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.assignFilesToVectorStore(
      id,
      assignFilesDto,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Đã nhận file ID từ RAG API và gán vào vector store thành công.'
    );
  }

  /**
   * Xóa file khỏi vector store
   */
  @ApiOperation({
    summary: 'Xóa file khỏi vector store',
    description: 'Xóa một file cụ thể khỏi vector store'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiParam({
    name: 'fileId',
    description: 'ID của file cần xóa',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file khỏi vector store thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Xóa file khỏi vector store thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string', example: 'File đã được xóa khỏi vector store' }
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_REMOVE_FILES_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Delete(':id/files/:fileId')
  @HttpCode(HttpStatus.OK)
  async removeFileFromVectorStore(
    @Param('id') id: string,
    @Param('fileId') fileId: string,
    @CurrentUser ('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.removeFilesFromVectorStore(
      id,
      [fileId],
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Xóa file khỏi vector store thành công.'
    );
  }

  /**
   * Xóa nhiều file khỏi vector store
   */
  @ApiOperation({
    summary: 'Xóa nhiều file khỏi vector store',
    description: 'Xóa nhiều file cùng lúc khỏi vector store'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiBody({
    type: AssignFilesDto,
    description: 'Danh sách ID của các file cần xóa',
    examples: {
      'single': {
        summary: 'Xóa một file',
        description: 'Xóa một file khỏi vector store',
        value: {
          fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890']
        }
      },
      'multiple': {
        summary: 'Xóa nhiều file',
        description: 'Xóa nhiều file khỏi vector store',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file khỏi vector store thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Xóa file khỏi vector store thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            removedCount: { type: 'number', example: 2 },
            failedItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' },
                  reason: { type: 'string', example: 'File không tồn tại trong vector store' }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_REMOVE_FILES_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Delete(':id/files')
  @HttpCode(HttpStatus.OK)
  async removeFilesFromVectorStore(
    @Param('id') id: string,
    @Body() assignFilesDto: AssignFilesDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.removeFilesFromVectorStore(
      id,
      assignFilesDto.fileIds,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Xóa file khỏi vector store thành công.'
    );
  }

  /**
   * Xóa nhiều vector store
   */
  @ApiOperation({ summary: 'Xóa nhiều vector store' })
  @ApiBody({
    type: DeleteVectorStoresDto,
    description: 'Danh sách ID của các vector store cần xóa',
    examples: {
      'single': {
        summary: 'Xóa một vector store',
        description: 'Xóa một vector store duy nhất',
        value: {
          storeIds: ['vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890']
        }
      },
      'multiple': {
        summary: 'Xóa nhiều vector store',
        description: 'Xóa nhiều vector store cùng lúc',
        value: {
          storeIds: [
            'vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'vs_b2c3d4e5-f6a7-8901-bcde-f01234567890',
            'vs_c3d4e5f6-a7b8-9012-cdef-012345678901'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa vector store thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Xóa vector store thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            deletedCount: { type: 'number', example: 2 },
            failedItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'vs_c3d4e5f6-a7b8-9012-cdef-012345678901' },
                  reason: { type: 'string', example: 'Vector store không tồn tại hoặc bạn không có quyền truy cập' }
                }
              },
              example: [
                {
                  id: 'vs_c3d4e5f6-a7b8-9012-cdef-012345678901',
                  reason: 'Vector store không tồn tại hoặc bạn không có quyền truy cập'
                }
              ]
            }
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Delete('batch')
  @HttpCode(HttpStatus.OK)
  async deleteVectorStores(
    @Body() deleteVectorStoresDto: DeleteVectorStoresDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.deleteVectorStores(
      deleteVectorStoresDto,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Xóa vector store thành công.'
    );
  }
}
