const fs = require('fs');

// Đọc file swagger-user-schema.json
const swaggerData = JSON.parse(fs.readFileSync('swagger-user-schema.json', 'utf8'));

// Hàm để resolve tất cả $ref thành định nghĩa thực tế
function resolveRefs(obj, schemas, visited = new Set()) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => resolveRefs(item, schemas, visited));
  }
  
  // Nếu có $ref, thay thế bằng schema thực tế
  if (obj.$ref && typeof obj.$ref === 'string' && obj.$ref.startsWith('#/components/schemas/')) {
    const schemaName = obj.$ref.replace('#/components/schemas/', '');
    
    // Tránh circular reference
    if (visited.has(schemaName)) {
      return { type: 'object', description: `Circular reference to ${schemaName}` };
    }
    
    if (schemas[schemaName]) {
      visited.add(schemaName);
      const resolvedSchema = resolveRefs(schemas[schemaName], schemas, new Set(visited));
      visited.delete(schemaName);
      return resolvedSchema;
    } else {
      // Nếu không tìm thấy schema, tạo một schema mặc định
      console.warn(`Schema not found: ${schemaName}, creating default schema`);
      return {
        type: 'object',
        description: `Schema ${schemaName} not found`,
        properties: {
          id: { type: 'string', description: 'ID' },
          name: { type: 'string', description: 'Name' }
        }
      };
    }
  }
  
  // Xử lý allOf, oneOf, anyOf
  if (obj.allOf) {
    const resolved = obj.allOf.map(item => resolveRefs(item, schemas, visited));
    // Merge tất cả properties từ allOf
    const mergedProperties = {};
    const mergedRequired = [];
    let mergedType = 'object';
    let mergedDescription = '';
    
    resolved.forEach(schema => {
      if (schema.properties) {
        Object.assign(mergedProperties, schema.properties);
      }
      if (schema.required) {
        mergedRequired.push(...schema.required);
      }
      if (schema.type) {
        mergedType = schema.type;
      }
      if (schema.description) {
        mergedDescription += (mergedDescription ? ' ' : '') + schema.description;
      }
    });
    
    return {
      type: mergedType,
      description: mergedDescription || 'Merged schema from allOf',
      properties: mergedProperties,
      ...(mergedRequired.length > 0 && { required: [...new Set(mergedRequired)] }),
      ...Object.fromEntries(Object.entries(obj).filter(([key]) => key !== 'allOf'))
    };
  }
  
  if (obj.oneOf) {
    return {
      oneOf: obj.oneOf.map(item => resolveRefs(item, schemas, visited)),
      ...Object.fromEntries(Object.entries(obj).filter(([key]) => key !== 'oneOf'))
    };
  }
  
  if (obj.anyOf) {
    return {
      anyOf: obj.anyOf.map(item => resolveRefs(item, schemas, visited)),
      ...Object.fromEntries(Object.entries(obj).filter(([key]) => key !== 'anyOf'))
    };
  }
  
  // Xử lý các thuộc tính khác
  const result = {};
  for (const [key, value] of Object.entries(obj)) {
    result[key] = resolveRefs(value, schemas, visited);
  }
  
  return result;
}

// Tạo bản sao của swagger data
const resolvedSwagger = JSON.parse(JSON.stringify(swaggerData));

// Resolve tất cả refs trong paths
console.log('🔄 Đang resolve references trong paths...');
resolvedSwagger.paths = resolveRefs(swaggerData.paths, swaggerData.components?.schemas || {});

// Resolve tất cả refs trong components/schemas
console.log('🔄 Đang resolve references trong schemas...');
if (resolvedSwagger.components?.schemas) {
  const resolvedSchemas = {};
  for (const [schemaName, schema] of Object.entries(resolvedSwagger.components.schemas)) {
    resolvedSchemas[schemaName] = resolveRefs(schema, swaggerData.components.schemas);
  }
  resolvedSwagger.components.schemas = resolvedSchemas;
}

// Xóa components.schemas vì đã resolve hết
delete resolvedSwagger.components.schemas;

// Ghi file mới
fs.writeFileSync('swagger-user-schema-resolved.json', JSON.stringify(resolvedSwagger, null, 2));

console.log('✅ Đã tạo file swagger-user-schema-resolved.json thành công!');
console.log('📝 Tất cả $ref đã được thay thế bằng định nghĩa schema thực tế');
console.log(`📊 File size: ${(fs.statSync('swagger-user-schema-resolved.json').size / 1024 / 1024).toFixed(2)} MB`);
